import React, { useState, useEffect, useRef } from 'react';
import cryptoService from '../../services/cryptoService.js';
import withdrawalService from '../../services/withdrawalService.js';
import { supabase } from '../../lib/supabase.js';
import '../styles/Withdraw.css';

const Withdraw = ({ user }) => {
  const [step, setStep] = useState(1); // 1: Amount, 2: Crypto & Address, 3: Confirmation
  const [amount, setAmount] = useState('');
  const [selectedCrypto, setSelectedCrypto] = useState(null);
  const [withdrawalAddress, setWithdrawalAddress] = useState('');
  const [supportedCryptos, setSupportedCryptos] = useState([]);
  const [currentWithdrawal, setCurrentWithdrawal] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [withdrawalHistory, setWithdrawalHistory] = useState([]);
  const [withdrawalStatusUpdates, setWithdrawalStatusUpdates] = useState({});
  const subscriptionRef = useRef(null);

  useEffect(() => {
    // Load supported cryptocurrencies
    const cryptos = cryptoService.getSupportedCryptocurrencies();
    setSupportedCryptos(cryptos);

    // Load withdrawal history
    loadWithdrawalHistory();

    // Set up real-time subscription for withdrawal updates
    if (user?.id) {
      setupWithdrawalSubscription();
    }

    // Cleanup on unmount
    return () => {
      if (subscriptionRef.current) {
        subscriptionRef.current.unsubscribe();
      }
    };
  }, [user]);

  const loadWithdrawalHistory = async () => {
    if (user?.id) {
      try {
        const authId = user.auth_id || user.id;
        const history = await withdrawalService.getUserWithdrawals(authId, 5);
        setWithdrawalHistory(history);
      } catch (error) {
        console.error('Error loading withdrawal history:', error);
      }
    }
  };

  const setupWithdrawalSubscription = () => {
    const authId = user.auth_id || user.id;
    subscriptionRef.current = withdrawalService.subscribeToWithdrawalUpdates(
      authId,
      (payload) => {
        console.log('Withdrawal update:', payload);
        
        if (payload.eventType === 'UPDATE') {
          setWithdrawalStatusUpdates(prev => ({
            ...prev,
            [payload.new.id]: payload.new
          }));
          
          // Reload history to show updated status
          loadWithdrawalHistory();
        }
      }
    );
  };

  const handleAmountSubmit = (e) => {
    e.preventDefault();
    setError('');

    const amountNum = parseFloat(amount);
    if (!amountNum || amountNum < 50) {
      setError('Minimum withdrawal amount is $50');
      return;
    }

    if (amountNum > (user?.balance || 0)) {
      setError('Insufficient balance for withdrawal');
      return;
    }

    setStep(2);
  };

  const handleCryptoAndAddressSubmit = (e) => {
    e.preventDefault();
    setError('');

    if (!selectedCrypto) {
      setError('Please select a cryptocurrency');
      return;
    }

    if (!withdrawalAddress.trim()) {
      setError('Please enter a withdrawal address');
      return;
    }

    // Validate address format
    if (!withdrawalService.validateCryptoAddress(withdrawalAddress.trim(), selectedCrypto.symbol)) {
      setError(`Invalid ${selectedCrypto.name} address format`);
      return;
    }

    setStep(3);
  };

  const createWithdrawalRequest = async () => {
    setLoading(true);
    setError('');

    try {
      const authId = user.auth_id || user.id;
      const withdrawal = await withdrawalService.createWithdrawal(
        authId,
        parseFloat(amount),
        selectedCrypto.id,
        withdrawalAddress.trim()
      );

      setCurrentWithdrawal(withdrawal);
      loadWithdrawalHistory(); // Refresh history
    } catch (error) {
      setError(error.message || 'Failed to create withdrawal request');
      setStep(2); // Go back to crypto & address step
    } finally {
      setLoading(false);
    }
  };

  const resetWithdrawal = () => {
    setStep(1);
    setAmount('');
    setSelectedCrypto(null);
    setWithdrawalAddress('');
    setCurrentWithdrawal(null);
    setError('');
  };

  const renderAmountStep = () => {
    return (
      <div className="withdraw-step">
        <div className="withdraw-step-header">
          <h2>Withdrawal Amount</h2>
          <p>Enter the amount you want to withdraw from your account</p>
          <div className="withdraw-balance-info">
            <span>Available Balance: <strong>${(user?.balance || 0).toFixed(2)} USD</strong></span>
          </div>
        </div>

        <form onSubmit={handleAmountSubmit} className="withdraw-amount-form">
          <div className="withdraw-amount-input-container">
            <span className="withdraw-currency-symbol">$</span>
            <input
              type="number"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              placeholder="50.00"
              min="50"
              max={user?.balance || 0}
              step="0.01"
              className="withdraw-amount-input"
              required
            />
            <span className="withdraw-currency-label">USD</span>
          </div>

          {error && <div className="withdraw-error">{error}</div>}

          <button type="submit" className="withdraw-continue-btn">
            Continue
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <polyline points="9,18 15,12 9,6"/>
            </svg>
          </button>
        </form>

        <div className="withdraw-quick-amounts">
          <span>Quick amounts:</span>
          {[50, 100, 250, 500].map(quickAmount => (
            <button
              key={quickAmount}
              onClick={() => setAmount(quickAmount.toString())}
              className="withdraw-quick-amount-btn"
              disabled={quickAmount > (user?.balance || 0)}
            >
              ${quickAmount}
            </button>
          ))}
        </div>
      </div>
    );
  };

  const renderCryptoAndAddressStep = () => {
    return (
      <div className="withdraw-step">
        <div className="withdraw-step-header">
          <button onClick={() => setStep(1)} className="withdraw-back-btn">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <polyline points="15,18 9,12 15,6"/>
            </svg>
            Back
          </button>
          <h2>Select Cryptocurrency & Address</h2>
          <p>Choose your preferred cryptocurrency and enter your wallet address for your ${amount} withdrawal</p>
        </div>

        <form onSubmit={handleCryptoAndAddressSubmit} className="withdraw-crypto-form">
          <div className="withdraw-crypto-selection">
            <h3>Select Cryptocurrency</h3>
            <div className="withdraw-crypto-grid">
              {supportedCryptos.map((crypto) => (
                <div
                  key={crypto.id}
                  className={`withdraw-crypto-option ${selectedCrypto?.id === crypto.id ? 'selected' : ''}`}
                  onClick={() => setSelectedCrypto(crypto)}
                >
                  <div className="withdraw-crypto-icon">
                    <img src={crypto.icon} alt={crypto.name} />
                  </div>
                  <div className="withdraw-crypto-info">
                    <h4>{crypto.name}</h4>
                    <span>{crypto.symbol}</span>
                  </div>
                  <div className="withdraw-crypto-rate">
                    <span>~{crypto.estimatedAmount || '0'} {crypto.symbol}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {selectedCrypto && (
            <div className="withdraw-address-section">
              <h3>Withdrawal Address</h3>
              <div className="withdraw-address-input-container">
                <input
                  type="text"
                  value={withdrawalAddress}
                  onChange={(e) => setWithdrawalAddress(e.target.value)}
                  placeholder={`Enter your ${selectedCrypto.name} wallet address`}
                  className="withdraw-address-input"
                  required
                />
              </div>
              <div className="withdraw-address-note">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <circle cx="12" cy="12" r="10"/>
                  <path d="M12 16v-4"/>
                  <path d="M12 8h.01"/>
                </svg>
                <span>Make sure the address is correct. Withdrawals to wrong addresses cannot be recovered.</span>
              </div>
            </div>
          )}

          {error && <div className="withdraw-error">{error}</div>}

          <button type="submit" className="withdraw-continue-btn" disabled={!selectedCrypto || !withdrawalAddress.trim()}>
            Continue
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <polyline points="9,18 15,12 9,6"/>
            </svg>
          </button>
        </form>
      </div>
    );
  };

  const renderConfirmationStep = () => {
    if (currentWithdrawal) {
      return renderWithdrawalSuccess();
    }

    return (
      <div className="withdraw-step">
        <div className="withdraw-step-header">
          <button onClick={() => setStep(2)} className="withdraw-back-btn">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <polyline points="15,18 9,12 15,6"/>
            </svg>
            Back
          </button>
          <h2>Confirm Withdrawal</h2>
          <p>Please review your withdrawal details before submitting</p>
        </div>

        <div className="withdraw-confirmation-details">
          <div className="withdraw-confirmation-card">
            <h3>Withdrawal Summary</h3>
            <div className="withdraw-confirmation-row">
              <span>Amount:</span>
              <strong>${amount} USD</strong>
            </div>
            <div className="withdraw-confirmation-row">
              <span>Cryptocurrency:</span>
              <strong>{selectedCrypto?.name} ({selectedCrypto?.symbol})</strong>
            </div>
            <div className="withdraw-confirmation-row">
              <span>Withdrawal Address:</span>
              <strong className="withdraw-address-display">{withdrawalAddress}</strong>
            </div>
            <div className="withdraw-confirmation-row">
              <span>Processing Time:</span>
              <strong>1-3 business days</strong>
            </div>
          </div>

          {error && <div className="withdraw-error">{error}</div>}

          <div className="withdraw-confirmation-actions">
            <button
              onClick={createWithdrawalRequest}
              disabled={loading}
              className="withdraw-confirm-btn"
            >
              {loading ? 'Processing...' : 'Confirm Withdrawal'}
            </button>
          </div>
        </div>
      </div>
    );
  };

  const renderWithdrawalSuccess = () => {
    return (
      <div className="withdraw-step">
        <div className="withdraw-step-header">
          <button onClick={resetWithdrawal} className="withdraw-back-btn">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <polyline points="15,18 9,12 15,6"/>
            </svg>
            New Withdrawal
          </button>
          <h2>Withdrawal Request Submitted</h2>
          <p>Your withdrawal request has been submitted successfully</p>
        </div>

        <div className="withdraw-success-content">
          <div className="withdraw-success-icon">
            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
              <polyline points="22,4 12,14.01 9,11.01"/>
            </svg>
          </div>

          <div className="withdraw-success-details">
            <h3>Request Details</h3>
            <div className="withdraw-success-row">
              <span>Amount:</span>
              <strong>${currentWithdrawal.amount_usd} USD</strong>
            </div>
            <div className="withdraw-success-row">
              <span>Cryptocurrency:</span>
              <strong>{currentWithdrawal.formatted_amount} {currentWithdrawal.cryptocurrency}</strong>
            </div>
            <div className="withdraw-success-row">
              <span>Status:</span>
              <strong className="status-pending">Pending Review</strong>
            </div>
          </div>

          <div className="withdraw-success-info">
            <h3>What's Next?</h3>
            <ol>
              <li>Your withdrawal request is being reviewed by our team</li>
              <li>You'll receive an email notification once processed</li>
              <li>Processing typically takes 1-3 business days</li>
              <li>You can track the status in your withdrawal history</li>
            </ol>
          </div>
        </div>
      </div>
    );
  };

  const renderWithdrawalHistory = () => {
    if (withdrawalHistory.length === 0) return null;

    return (
      <div className="withdraw-history">
        <h3>Recent Withdrawals</h3>
        <div className="withdraw-history-list">
          {withdrawalHistory.map((withdrawal) => {
            // Check for status updates
            const updatedWithdrawal = withdrawalStatusUpdates[withdrawal.id] || withdrawal;

            return (
              <div key={withdrawal.id} className="withdraw-history-item">
                <div className="withdraw-history-header">
                  <span className="withdraw-history-amount">
                    ${updatedWithdrawal.amount_usd}
                  </span>
                  <span className={`withdraw-history-status ${updatedWithdrawal.status}`}>
                    {updatedWithdrawal.status}
                  </span>
                </div>
                <div className="withdraw-history-details">
                  <span className="withdraw-history-crypto">
                    {updatedWithdrawal.crypto_amount} {updatedWithdrawal.cryptocurrency}
                  </span>
                  <span className="withdraw-history-date">
                    {new Date(updatedWithdrawal.created_at).toLocaleDateString()}
                  </span>
                </div>
                {updatedWithdrawal.withdrawal_address && (
                  <div className="withdraw-history-address">
                    <span>To: {updatedWithdrawal.withdrawal_address.substring(0, 8)}...{updatedWithdrawal.withdrawal_address.substring(-8)}</span>
                  </div>
                )}
                {updatedWithdrawal.transaction_hash && (
                  <div className="withdraw-history-tx">
                    <span>TX: {updatedWithdrawal.transaction_hash.substring(0, 8)}...{updatedWithdrawal.transaction_hash.substring(-8)}</span>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div className="withdraw">
      <div className="withdraw-header">
        <h1>Withdraw Funds</h1>
        <p>Withdraw your BullSeed account balance to your cryptocurrency wallet</p>
      </div>

      <div className="withdraw-content">
        <div className="withdraw-main">
          {step === 1 && renderAmountStep()}
          {step === 2 && renderCryptoAndAddressStep()}
          {step === 3 && renderConfirmationStep()}
        </div>

        <div className="withdraw-sidebar">
          {renderWithdrawalHistory()}

          <div className="withdraw-support">
            <h3>Need Help?</h3>
            <p>If you're having trouble with your withdrawal, our support team is here to help.</p>
            <button className="withdraw-support-btn">
              Contact Support
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Withdraw;
