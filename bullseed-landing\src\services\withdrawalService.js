import { supabase } from '../lib/supabase.js';
import cryptoService from './cryptoService.js';

class WithdrawalService {
  // Create a new withdrawal request
  async createWithdrawal(userId, usdAmount, cryptoId, withdrawalAddress) {
    try {
      // Validate inputs
      if (!userId || !usdAmount || !cryptoId || !withdrawalAddress) {
        throw new Error('Missing required parameters');
      }

      if (usdAmount < 50) {
        throw new Error('Minimum withdrawal amount is $50');
      }

      // Get crypto details
      const crypto = cryptoService.getSupportedCryptocurrencies().find(c => c.id === cryptoId);
      if (!crypto) {
        throw new Error('Unsupported cryptocurrency');
      }

      // Check user balance
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('balance')
        .eq('auth_id', userId)
        .single();

      if (userError) {
        throw new Error('Failed to verify user balance');
      }

      if (!userData || userData.balance < usdAmount) {
        throw new Error('Insufficient balance for withdrawal');
      }

      // Convert USD to crypto amount
      const conversion = await cryptoService.convertUsdToCrypto(usdAmount, cryptoId);
      if (!conversion) {
        throw new Error('Failed to get current exchange rate');
      }

      // Validate withdrawal address format (basic validation)
      if (!this.validateCryptoAddress(withdrawalAddress, crypto.symbol)) {
        throw new Error(`Invalid ${crypto.name} address format`);
      }

      // Immediately deduct balance (pending withdrawal)
      const newBalance = currentBalance - usdAmount;
      const { error: balanceError } = await supabase
        .from('users')
        .update({
          balance: newBalance,
          updated_at: new Date().toISOString()
        })
        .eq('auth_id', userId);

      if (balanceError) {
        throw new Error('Failed to update user balance');
      }

      // Create withdrawal record in database
      const { data: withdrawal, error } = await supabase
        .from('crypto_withdrawals')
        .insert({
          user_id: userId,
          amount_usd: usdAmount,
          cryptocurrency: crypto.symbol,
          crypto_amount: conversion.cryptoAmount,
          withdrawal_address: withdrawalAddress,
          status: 'pending'
        })
        .select()
        .single();

      if (error) {
        console.error('Database error creating withdrawal:', error);
        // Rollback balance change if withdrawal creation failed
        await supabase
          .from('users')
          .update({
            balance: currentBalance,
            updated_at: new Date().toISOString()
          })
          .eq('auth_id', userId);
        throw new Error('Failed to create withdrawal request');
      }

      // Create pending transaction record
      const { error: transactionError } = await supabase
        .from('transactions')
        .insert({
          user_id: userId,
          type: 'withdrawal',
          amount: -usdAmount, // Negative for withdrawal
          status: 'pending',
          description: `${crypto.name} Withdrawal (Pending) - ${conversion.formattedAmount} ${crypto.symbol}`,
          created_at: new Date().toISOString()
        });

      if (transactionError) {
        console.error('Error creating transaction record:', transactionError);
        // Don't fail the withdrawal for transaction record error, just log it
      }

      // Return withdrawal with formatted data
      return {
        ...withdrawal,
        formatted_amount: conversion.formattedAmount,
        exchange_rate: conversion.exchangeRate,
        crypto_name: crypto.name,
        crypto_symbol: crypto.symbol
      };

    } catch (error) {
      console.error('Error creating withdrawal:', error);
      throw error;
    }
  }

  // Get user withdrawals
  async getUserWithdrawals(userId, limit = 10) {
    try {
      const { data, error } = await supabase
        .from('crypto_withdrawals')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching user withdrawals:', error);
        throw new Error('Failed to fetch withdrawal history');
      }

      return data || [];
    } catch (error) {
      console.error('Error in getUserWithdrawals:', error);
      throw error;
    }
  }

  // Get withdrawal by ID
  async getWithdrawal(withdrawalId) {
    try {
      const { data, error } = await supabase
        .from('crypto_withdrawals')
        .select('*')
        .eq('id', withdrawalId)
        .single();

      if (error) {
        console.error('Error fetching withdrawal:', error);
        throw new Error('Withdrawal not found');
      }

      return data;
    } catch (error) {
      console.error('Error in getWithdrawal:', error);
      throw error;
    }
  }

  // Update withdrawal status
  async updateWithdrawalStatus(withdrawalId, status, transactionHash = null, adminNotes = null) {
    try {
      const updateData = {
        status,
        updated_at: new Date().toISOString()
      };

      if (transactionHash) {
        updateData.transaction_hash = transactionHash;
      }

      if (adminNotes) {
        updateData.admin_notes = adminNotes;
      }

      if (status === 'completed' || status === 'failed') {
        updateData.processed_at = new Date().toISOString();
      }

      const { data, error } = await supabase
        .from('crypto_withdrawals')
        .update(updateData)
        .eq('id', withdrawalId)
        .select()
        .single();

      if (error) {
        console.error('Error updating withdrawal status:', error);
        throw new Error('Failed to update withdrawal status');
      }

      return data;
    } catch (error) {
      console.error('Error in updateWithdrawalStatus:', error);
      throw error;
    }
  }

  // Basic crypto address validation
  validateCryptoAddress(address, cryptocurrency) {
    if (!address || typeof address !== 'string') {
      return false;
    }

    // Remove whitespace
    address = address.trim();

    switch (cryptocurrency.toLowerCase()) {
      case 'btc':
      case 'bitcoin':
        // Bitcoin address validation (basic)
        return /^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$|^bc1[a-z0-9]{39,59}$/.test(address);
      
      case 'eth':
      case 'ethereum':
      case 'bnb':
        // Ethereum/BNB address validation (basic)
        return /^0x[a-fA-F0-9]{40}$/.test(address);
      
      case 'ltc':
      case 'litecoin':
        // Litecoin address validation (basic)
        return /^[LM3][a-km-zA-HJ-NP-Z1-9]{26,33}$|^ltc1[a-z0-9]{39,59}$/.test(address);
      
      default:
        // For other cryptocurrencies, just check if it's not empty
        return address.length > 10;
    }
  }

  // Get withdrawal statistics for admin
  async getWithdrawalStats() {
    try {
      const { data, error } = await supabase
        .from('crypto_withdrawals')
        .select('status, amount_usd, created_at');

      if (error) {
        console.error('Error fetching withdrawal stats:', error);
        return {
          total: 0,
          pending: 0,
          completed: 0,
          failed: 0,
          totalAmount: 0
        };
      }

      const stats = {
        total: data.length,
        pending: data.filter(w => w.status === 'pending').length,
        completed: data.filter(w => w.status === 'completed').length,
        failed: data.filter(w => w.status === 'failed').length,
        totalAmount: data.reduce((sum, w) => sum + parseFloat(w.amount_usd || 0), 0)
      };

      return stats;
    } catch (error) {
      console.error('Error in getWithdrawalStats:', error);
      return {
        total: 0,
        pending: 0,
        completed: 0,
        failed: 0,
        totalAmount: 0
      };
    }
  }

  // Subscribe to withdrawal updates for real-time notifications
  subscribeToWithdrawalUpdates(userId, callback) {
    const subscription = supabase
      .channel(`withdrawal_updates_${userId}`)
      .on('postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'crypto_withdrawals',
          filter: `user_id=eq.${userId}`
        },
        (payload) => {
          console.log('Withdrawal update received:', payload);
          if (callback) {
            callback(payload);
          }
        }
      )
      .subscribe();

    return subscription;
  }
}

const withdrawalService = new WithdrawalService();
export default withdrawalService;
